from odoo import models, fields, api, _
from odoo.exceptions import UserError
from collections import defaultdict
import base64
import xlsxwriter
import io


class CustomerBalanceByBranchWizard(models.TransientModel):
    _name = 'customer.balance.by.branch.wizard'
    _description = 'Customer Balance By Branch Report Wizard'

    branch_id = fields.Many2one(
        'company.branch',
        string='الفرع',
        required=False,
        help='اختر الفرع لتصفية العملاء، أو اتركه فارغاً لعرض جميع الفروع'
    )
    exclude_zero_balance = fields.Boolean(
        string='استبعاد العملاء بأرصدة صفر أو أقل',
        default=True,
        help='إذا تم تفعيل هذا الخيار، سيتم استبعاد العملاء الذين لديهم أرصدة صفر أو سالبة من التقرير'
    )
    excel_file = fields.Binary('تقرير Excel', readonly=True)
    file_name = fields.Char('اسم الملف', readonly=True)

    def action_print_report(self):
        """Generate the customer balance report"""
        self.ensure_one()
        
        # Get all customers related to the selected branch
        customers_data = self._get_customers_by_branch()
        
        if not customers_data:
            if self.branch_id:
                raise UserError(_('لم يتم العثور على عملاء للفرع المحدد.'))
            else:
                raise UserError(_('لم يتم العثور على عملاء بأرصدة مستحقة.'))
        
        # Generate PDF report
        return {
            'type': 'ir.actions.report',
            'report_name': 'customer_balance_by_branch.customer_balance_report',
            'report_type': 'qweb-pdf',
            'data': {
                'wizard_id': self.id,
                'branch_id': self.branch_id.id if self.branch_id else False,
                'branch_name': self.branch_id.name if self.branch_id else 'جميع الفروع',
                'customers_data': customers_data,
            },
            'context': self.env.context,
        }

    def action_export_excel(self):
        """Export customer balance data to Excel"""
        self.ensure_one()

        # Get all customers related to the selected branch
        customers_data = self._get_customers_by_branch()

        if not customers_data:
            if self.branch_id:
                raise UserError(_('لم يتم العثور على عملاء للفرع المحدد.'))
            else:
                raise UserError(_('لم يتم العثور على عملاء بأرصدة مستحقة.'))

        # Create Excel file
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        worksheet = workbook.add_worksheet('Customer Balance Report')

        # Define formats
        header_format = workbook.add_format({
            'bold': True,
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#4472C4',
            'font_color': 'white',
            'border': 1
        })

        title_format = workbook.add_format({
            'bold': True,
            'font_size': 16,
            'align': 'center',
            'valign': 'vcenter'
        })

        cell_format = workbook.add_format({
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })

        number_format = workbook.add_format({
            'align': 'right',
            'valign': 'vcenter',
            'border': 1,
            'num_format': '#,##0.00'
        })

        total_format = workbook.add_format({
            'bold': True,
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'bg_color': '#FFF2CC',
            'num_format': '#,##0.00'
        })

        # Set column widths
        worksheet.set_column('A:A', 8)   # Row Number
        worksheet.set_column('B:B', 30)  # Customer Name
        worksheet.set_column('C:C', 20)  # Phone
        if not self.branch_id:  # All branches
            worksheet.set_column('D:D', 20)  # Branch
            worksheet.set_column('E:E', 15)  # Balance
        else:  # Single branch
            worksheet.set_column('D:D', 15)  # Balance

        # Set RTL
        worksheet.right_to_left()

        # Write title
        branch_name = self.branch_id.name if self.branch_id else 'جميع الفروع'
        worksheet.merge_range('A1:E1' if not self.branch_id else 'A1:D1',
                            f'تقرير أرصدة العملاء - {branch_name}', title_format)

        # Write summary
        total_customers = customers_data.get('customers_count', 0)
        total_due = customers_data.get('total_due', 0)

        if not self.branch_id:  # All branches
            worksheet.merge_range('A2:C2', f'إجمالي العملاء: {total_customers}', cell_format)
            worksheet.merge_range('D2:E2', f'إجمالي المديونية: {total_due:,.2f}', cell_format)
        else:  # Single branch
            worksheet.merge_range('A2:B2', f'إجمالي العملاء: {total_customers}', cell_format)
            worksheet.merge_range('C2:D2', f'إجمالي المديونية: {total_due:,.2f}', cell_format)

        # Write headers
        row = 3
        if not self.branch_id:  # All branches
            headers = ['#', 'اسم العميل', 'رقم الهاتف', 'الفرع الأساسي', 'الرصيد الحالي']
        else:  # Single branch
            headers = ['#', 'اسم العميل', 'رقم الهاتف', 'الرصيد الحالي']

        for col, header in enumerate(headers):
            worksheet.write(row, col, header, header_format)

        # Write customer data
        row += 1
        for customer in customers_data.get('customers', []):
            col = 0
            # Row number
            worksheet.write(row, col, customer.get('row_number', ''), cell_format)
            col += 1
            # Customer name
            worksheet.write(row, col, customer.get('name', ''), cell_format)
            col += 1
            # Phone
            worksheet.write(row, col, customer.get('phone', ''), cell_format)
            col += 1

            if not self.branch_id:  # All branches - show branch column
                worksheet.write(row, col, customer.get('branch_name', ''), cell_format)
                col += 1

            # Balance
            worksheet.write(row, col, customer.get('current_balance', 0), number_format)
            row += 1

        # Write total row
        if not self.branch_id:  # All branches
            worksheet.merge_range(row, 0, row, 3, 'الإجمالي', total_format)
            worksheet.write(row, 4, total_due, total_format)
        else:  # Single branch
            worksheet.merge_range(row, 0, row, 2, 'الإجمالي', total_format)
            worksheet.write(row, 3, total_due, total_format)

        workbook.close()

        # Set the generated file
        file_data = base64.b64encode(output.getvalue())
        file_name = f"Customer_Balance_Report_{branch_name}_{self.env.context.get('tz_offset', '')}.xlsx"

        # Save the data to the record
        self.write({
            'excel_file': file_data,
            'file_name': file_name,
        })

        # Return direct download action
        return {
            'type': 'ir.actions.act_url',
            'url': f"/web/content/customer.balance.by.branch.wizard/{self.id}/excel_file/{file_name}?download=true",
            'target': 'self',
        }

    def _get_customers_by_branch(self):
        """
        Get ALL customers with ANY balance (including zero), regardless of invoice history or other filters.
        For customers with invoice history, assign them to their primary branch.
        For customers without invoice history, assign them to "غير محدد" branch.

        Returns ALL customers with any account move lines in receivable accounts.
        """
        # First, get ALL customers with any balance (positive, negative, or zero)
        # Get all customers who have any account move lines in receivable accounts
        self.env.cr.execute("""
            SELECT DISTINCT aml.partner_id,
                   COALESCE(SUM(aml.debit) - SUM(aml.credit), 0) as balance
            FROM account_move_line aml
            JOIN account_account aa ON aml.account_id = aa.id
            JOIN account_move am ON aml.move_id = am.id
            JOIN res_partner rp ON aml.partner_id = rp.id
            WHERE aa.account_type = 'asset_receivable'
            AND am.state = 'posted'
            AND aml.partner_id IS NOT NULL
            GROUP BY aml.partner_id

        """)

        customers_with_balance = self.env.cr.fetchall()
        all_customer_ids = [row[0] for row in customers_with_balance]

        if not all_customer_ids:
            return {
                'customers': [],
                'total_due': 0.0,
                'customers_count': 0,
            }

        # Now get branch assignment for customers who have invoice history
        # Build domain for invoice search (all posted invoices) - no customer filters
        domain = [
            ('move_type', 'in', ['out_invoice', 'out_refund']),
            ('state', 'in', ['posted']),
            ('partner_id', 'in', all_customer_ids),  # Only customers with balances
        ]

        # Search for invoices with branch information
        invoices = self.env['account.move'].search(domain)

        # Group invoices by customer and branch
        customer_branch_totals = defaultdict(lambda: defaultdict(float))

        for invoice in invoices:
            if invoice.branch_id:  # Only consider invoices with branch
                partner_id = invoice.partner_id.id
                branch_id = invoice.branch_id.id

                # Calculate invoice total (positive for invoices, negative for refunds)
                amount = invoice.amount_total_signed
                customer_branch_totals[partner_id][branch_id] += amount

        # Determine primary branch for each customer
        customer_primary_branch = {}
        for partner_id, branch_totals in customer_branch_totals.items():
            # Find the branch with the highest total amount
            primary_branch_id = max(branch_totals.items(), key=lambda x: x[1])[0]
            customer_primary_branch[partner_id] = primary_branch_id

        # Filter customers for the selected branch (or all branches if none selected)
        if self.branch_id:
            # Include customers assigned to this branch OR customers with no branch assignment
            customers_for_branch = []
            for customer_id in all_customer_ids:
                primary_branch_id = customer_primary_branch.get(customer_id)
                if primary_branch_id == self.branch_id.id:
                    customers_for_branch.append(customer_id)
                elif primary_branch_id is None:
                    # Customer has no branch assignment - include in selected branch
                    customers_for_branch.append(customer_id)
        else:
            # Include ALL customers with balances
            customers_for_branch = all_customer_ids
        
        # Get customer balance information
        customers_data = []
        total_due = 0.0

        for partner_id in customers_for_branch:
            partner = self.env['res.partner'].browse(partner_id)

            # Calculate current due balance using proper accounting formula
            # For customers (receivables): debit - credit = amount customer owes
            # Use SQL query for accurate balance calculation from account move lines
            self.env.cr.execute("""
                SELECT COALESCE(SUM(aml.debit) - SUM(aml.credit), 0) as balance
                FROM account_move_line aml
                JOIN account_account aa ON aml.account_id = aa.id
                JOIN account_move am ON aml.move_id = am.id
                WHERE aml.partner_id = %s
                AND aa.account_type = 'asset_receivable'
                AND am.state = 'posted'
            """, (partner.id,))

            result = self.env.cr.fetchone()
            current_balance = result[0] if result and result[0] is not None else 0.0

            # Apply balance filter if exclude_zero_balance is enabled
            if self.exclude_zero_balance and current_balance <= 0:
                continue
                
            # Get phone number - use mobile if phone is empty, or both if both exist
            phone_number = ''
            if partner.phone and partner.mobile:
                phone_number = f"{partner.phone} / {partner.mobile}"
            elif partner.phone:
                phone_number = partner.phone
            elif partner.mobile:
                phone_number = partner.mobile
            
            # Get primary branch for this customer
            primary_branch = self._get_customer_primary_branch(partner.id)
            branch_name = primary_branch.name if primary_branch else 'غير محدد'

            customers_data.append({
                'id': partner.id,
                'name': partner.name,
                'phone': phone_number,
                'current_balance': current_balance,
                'branch_name': branch_name,
            })
            
            total_due += current_balance
        
        # Sort customers by balance (highest first)
        customers_data.sort(key=lambda x: x['current_balance'], reverse=True)

        # Add row numbers to each customer
        for index, customer in enumerate(customers_data, start=1):
            customer['row_number'] = index

        return {
            'customers': customers_data,
            'total_due': total_due,
            'customers_count': len(customers_data),
        }

    def _get_customer_primary_branch(self, partner_id):
        """Get the primary branch for a specific customer"""
        # Build domain for invoice search
        domain = [
            ('move_type', 'in', ['out_invoice', 'out_refund']),
            ('state', 'in', ['posted']),
            ('partner_id', '=', partner_id),
        ]

        # Search for invoices with branch information
        invoices = self.env['account.move'].search(domain)

        # Group invoices by branch
        branch_totals = defaultdict(float)

        for invoice in invoices:
            if invoice.branch_id:
                branch_id = invoice.branch_id.id
                amount = invoice.amount_total_signed
                branch_totals[branch_id] += amount

        if branch_totals:
            # Find the branch with the highest total amount
            primary_branch_id = max(branch_totals.items(), key=lambda x: x[1])[0]
            return self.env['company.branch'].browse(primary_branch_id)

        # Return None for customers without branch assignment
        return None

    def _get_report_data(self):
        """Get data for the PDF report"""
        customers_data = self._get_customers_by_branch()
        return {
            'wizard': self,
            'branch_name': self.branch_id.name if self.branch_id else 'جميع الفروع',
            'customers_data': customers_data,
        }


class CustomerBalanceByBranchReport(models.AbstractModel):
    _name = 'report.customer_balance_by_branch.customer_balance_report'
    _description = 'Customer Balance By Branch PDF Report'

    @api.model
    def _get_report_values(self, docids, data=None):
        """Get values for the PDF report"""
        if data and data.get('wizard_id'):
            wizard = self.env['customer.balance.by.branch.wizard'].browse(data['wizard_id'])
            customers_data = data.get('customers_data', {})
            
            return {
                'doc_ids': docids,
                'doc_model': 'customer.balance.by.branch.wizard',
                'docs': [wizard],  # Add docs for the template
                'data': data,
                'wizard': wizard,
                'branch_name': data.get('branch_name'),
                'customers_data': customers_data,
            }
        return {}



<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- PDF Report Definition -->
        <record id="customer_balance_by_branch_report_action" model="ir.actions.report">
            <field name="name">تقرير أرصدة العملاء حسب الفرع</field>
            <field name="model">customer.balance.by.branch.wizard</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">customer_balance_by_branch.customer_balance_report</field>
            <field name="report_file">customer_balance_by_branch.customer_balance_report</field>
            <field name="print_report_name">'Customer Balance - %s' % (object.branch_id.name,)</field>
            <field name="binding_model_id" ref="model_customer_balance_by_branch_wizard"/>
            <field name="binding_type">report</field>
        </record>

        <!-- QWeb Template for PDF Report -->
        <template id="customer_balance_report">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="doc">
                    <t t-call="web.external_layout">
                        <div class="page" style="font-family: 'Arial', sans-serif;">
                            <!-- Hide any unwanted header content -->
                            <style>
                                .o_report_layout_standard .header table,
                                .o_report_layout_standard .header .row:first-child {
                                    display: none !important;
                                }
                                .page {
                                    padding-top: 20px !important;
                                }
                            </style>
                            <!-- Report Title -->
                            <div class="row mb-4" style="direction: rtl;">
                                <div class="col-12">
                                    <h2 class="text-center">
                                        <strong>تقرير أرصدة العملاء</strong>
                                    </h2>
                                    <h3 class="text-center text-muted">
                                        الفرع: <span t-esc="branch_name"/>
                                    </h3>
                                </div>
                            </div>

                            <!-- Current Date -->
                            <div class="row mb-3" style="direction: rtl;">
                                <div class="col-12 text-center">
                                    <p><strong>تاريخ التقرير:</strong> <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%Y-%m-%d')"/></p>
                                </div>
                            </div>

                            <!-- Summary -->
                            <div class="row mb-4" style="direction: rtl;">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <div class="row">
                                            <div class="col-6">
                                                <strong>إجمالي العملاء:</strong> <span t-esc="customers_data.get('customers_count', 0)"/>
                                            </div>
                                            <div class="col-6 text-left">
                                                <strong>إجمالي المديونية:</strong> <span t-esc="'{:,.2f}'.format(customers_data.get('total_due', 0))"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Table -->
                            <div class="row" style="direction: rtl;">
                                <div class="col-12">
                                    <table class="table table-bordered table-striped">
                                        <thead style="background-color: #f8f9fa;">
                                            <tr>
                                                <th style="width: 50px;">#</th>
                                                <th>اسم العميل</th>
                                                <th>رقم الهاتف</th>
                                                <th t-if="branch_name == 'جميع الفروع'">الفرع الأساسي</th>
                                                <th class="text-left">الرصيد الحالي</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-foreach="customers_data.get('customers', [])" t-as="customer">
                                                <tr>
                                                    <td class="text-center"><span t-esc="customer.get('row_number', '')"/></td>
                                                    <td><span t-esc="customer.get('name', '')"/></td>
                                                    <td><span t-esc="customer.get('phone', '')"/></td>
                                                    <td t-if="branch_name == 'جميع الفروع'"><span t-esc="customer.get('branch_name', '')"/></td>
                                                    <td class="text-left">
                                                        <span t-att-style="'color: red;' if customer.get('current_balance', 0) &lt; 0 else 'color: green;' if customer.get('current_balance', 0) &gt; 0 else 'color: gray;'"
                                                              t-esc="'{:,.2f}'.format(customer.get('current_balance', 0))"/>
                                                    </td>
                                                </tr>
                                            </t>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Total Row - Separate from main table -->
                            <div class="row mt-3" style="direction: rtl;">
                                <div class="col-12">
                                    <table class="table table-bordered">
                                        <tr style="background-color: #fff3cd;">
                                            <th t-if="branch_name == 'جميع الفروع'" colspan="4"><strong>الإجمالي</strong></th>
                                            <th t-if="branch_name != 'جميع الفروع'" colspan="3"><strong>الإجمالي</strong></th>
                                            <th class="text-left">
                                                <strong><span t-esc="'{:,.2f}'.format(customers_data.get('total_due', 0))"/></strong>
                                            </th>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Footer Info -->
                            <div class="row mt-4" style="direction: rtl;">
                                <div class="col-12">
                                    <p class="text-muted small">
                                        <strong>ملاحظة:</strong> يتم تعيين العملاء للفروع بناءً على أعلى مبلغ إجمالي للمشتريات.
                                        الرصيد الحالي يمثل المبلغ المستحق من/على كل عميل من الحسابات المدينة المرحلة.
                                        يتم عرض جميع العملاء الذين لديهم حركات محاسبية (موجبة، سالبة، أو صفر).
                                        العملاء بدون تاريخ فواتير يظهرون تحت "غير محدد".
                                    </p>
                                </div>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </template>



    </data>
</odoo>
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Wizard Form View -->
        <record id="customer_balance_by_branch_wizard_form" model="ir.ui.view">
            <field name="name">customer.balance.by.branch.wizard.form</field>
            <field name="model">customer.balance.by.branch.wizard</field>
            <field name="arch" type="xml">
                <form string="تقرير أرصدة العملاء حسب الفرع">
                    <group>
                        <group string="خيارات التصفية">
                            <field name="branch_id" string="الفرع (اختياري - اتركه فارغاً لجميع الفروع)" options="{'no_create': True, 'no_open': True}"/>
                            <field name="exclude_zero_balance" string="استبعاد العملاء بأرصدة صفر أو أقل"/>
                        </group>
                    </group>
                    <footer>
                        <button name="action_print_report" string="طباعة التقرير PDF" type="object" class="btn-primary"/>
                        <button name="action_export_excel" string="تصدير إلى Excel" type="object" class="btn-success"/>
                        <button string="إلغاء" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Wizard Action -->
        <record id="customer_balance_by_branch_wizard_action" model="ir.actions.act_window">
            <field name="name">تقرير أرصدة العملاء حسب الفرع</field>
            <field name="res_model">customer.balance.by.branch.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="customer_balance_by_branch_wizard_form"/>
        </record>

    </data>
</odoo> 